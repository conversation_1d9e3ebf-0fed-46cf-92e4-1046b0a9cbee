import { Variants } from 'framer-motion'

// 🌱 HSSL Organic Eco-Friendly Color System
// Unified emerald-teal theme with clearly defined roles and accessibility focus
export const colorTheme = {
  // 🎯 Primary Actions - Light green for main CTAs
  primary: {
    gradient: 'from-green-300 to-green-400',
    gradientHover: 'from-green-400 to-green-500',
    bg: 'bg-green-300',
    bgHover: 'bg-green-400',
    bgActive: 'bg-green-500',
    text: 'text-green-600',
    textHover: 'text-green-700',
    light: 'bg-green-50',
    lightHover: 'bg-green-100',
    border: 'border-green-200',
    borderHover: 'border-green-300',
    ring: 'ring-green-500',
    shadow: 'shadow-green-500/25'
  },

  // 🌊 Secondary Actions - Light green variant for supporting elements
  secondary: {
    gradient: 'from-green-200 to-green-300',
    gradientHover: 'from-green-300 to-green-400',
    bg: 'bg-green-200',
    bgHover: 'bg-green-300',
    bgActive: 'bg-green-400',
    text: 'text-green-600',
    textHover: 'text-green-700',
    light: 'bg-green-50',
    lightHover: 'bg-green-100',
    border: 'border-green-200',
    borderHover: 'border-green-300',
    ring: 'ring-green-500',
    shadow: 'shadow-green-500/25'
  },

  // ✨ Success States - Light green for positive feedback
  success: {
    gradient: 'from-green-300 to-green-400',
    gradientHover: 'from-green-400 to-green-500',
    bg: 'bg-green-300',
    bgHover: 'bg-green-400',
    text: 'text-green-700',
    light: 'bg-green-50',
    border: 'border-green-200',
    ring: 'ring-green-500',
    shadow: 'shadow-green-500/20'
  },

  // ⚠️ Warning States - Light green for alerts
  warning: {
    gradient: 'from-green-300 to-green-400',
    gradientHover: 'from-green-400 to-green-500',
    bg: 'bg-green-300',
    bgHover: 'bg-green-400',
    text: 'text-green-700',
    light: 'bg-green-50',
    border: 'border-green-200',
    ring: 'ring-green-500',
    shadow: 'shadow-green-500/20'
  },

  // 🚨 Error States - Red for errors
  error: {
    gradient: 'from-red-400 to-red-500',
    gradientHover: 'from-red-500 to-red-600',
    bg: 'bg-red-500',
    bgHover: 'bg-red-600',
    text: 'text-red-700',
    light: 'bg-red-50',
    border: 'border-red-200',
    ring: 'ring-red-500',
    shadow: 'shadow-red-500/20'
  },

  // 🌿 Neutral/Tertiary - Cream backgrounds
  neutral: {
    gradient: 'from-yellow-50 to-yellow-100',
    gradientHover: 'from-yellow-100 to-yellow-200',
    bg: 'bg-yellow-50',
    bgHover: 'bg-yellow-100',
    text: 'text-slate-600',
    textHover: 'text-slate-700',
    light: 'bg-yellow-50',
    lightHover: 'bg-yellow-100',
    border: 'border-yellow-200',
    borderHover: 'border-yellow-300',
    ring: 'ring-yellow-500',
    shadow: 'shadow-yellow-500/10'
  },

  // 🌱 Accent/Contrast - Darker green for emphasis
  accent: {
    gradient: 'from-green-500 to-green-600',
    gradientHover: 'from-green-600 to-green-700',
    bg: 'bg-green-600',
    bgHover: 'bg-green-700',
    text: 'text-green-700',
    textHover: 'text-green-800',
    light: 'bg-green-50',
    lightHover: 'bg-green-100',
    border: 'border-green-300',
    borderHover: 'border-green-400',
    ring: 'ring-green-600',
    shadow: 'shadow-green-600/25'
  }
}

// 🌈 Recommended Gradient System for Different Use Cases
export const gradientSystem = {
  // Hero sections - Bold, eye-catching gradients
  hero: {
    primary: 'from-green-300 via-green-400 to-green-500',
    secondary: 'from-green-200 via-green-300 to-green-400',
    accent: 'from-green-500 via-green-600 to-green-700'
  },

  // Card backgrounds and overlays - Subtle, elegant
  cards: {
    primary: 'from-green-50 via-yellow-50 to-yellow-100',
    secondary: 'from-yellow-50 via-green-50 to-green-100',
    accent: 'from-yellow-50 via-green-50 to-yellow-50'
  },

  // Buttons and interactive elements - Vibrant, engaging
  interactive: {
    primary: 'from-green-300 to-green-400',
    secondary: 'from-green-200 to-green-300',
    accent: 'from-green-500 to-green-600',
    hover: {
      primary: 'from-green-400 to-green-500',
      secondary: 'from-green-300 to-green-400',
      accent: 'from-green-600 to-green-700'
    }
  },

  // Section backgrounds - Organic, flowing transitions
  sections: {
    light: 'from-green-50 via-yellow-50 to-yellow-100',
    medium: 'from-green-100 via-yellow-100 to-green-200',
    dark: 'from-green-700 via-green-800 to-green-900'
  }
}

// 🎨 Section Background Alternation Patterns
export const sectionPatterns = {
  // Standard alternating pattern
  standard: [
    'bg-white',
    'bg-gradient-to-br from-emerald-50 via-white to-teal-50',
    'bg-emerald-50',
    'bg-gradient-to-br from-teal-50 via-emerald-50 to-green-50'
  ],

  // Subtle gradient pattern
  subtle: [
    'bg-gradient-to-br from-white via-emerald-50 to-white',
    'bg-gradient-to-br from-emerald-50 via-white to-teal-50',
    'bg-gradient-to-br from-teal-50 via-emerald-50 to-white',
    'bg-gradient-to-br from-white via-teal-50 to-emerald-50'
  ],

  // Bold contrast pattern
  bold: [
    'bg-white',
    'bg-gradient-to-br from-emerald-500 to-teal-600',
    'bg-emerald-50',
    'bg-gradient-to-br from-teal-600 to-green-700'
  ]
}

// Enhanced animation variants
export const fadeInUp: Variants = {
  initial: { 
    opacity: 0, 
    y: 60,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const fadeInDown: Variants = {
  initial: { 
    opacity: 0, 
    y: -60,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const fadeInLeft: Variants = {
  initial: { 
    opacity: 0, 
    x: -60,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    x: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const fadeInRight: Variants = {
  initial: { 
    opacity: 0, 
    x: 60,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    x: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const scaleIn: Variants = {
  initial: { 
    opacity: 0, 
    scale: 0.8,
    rotate: -5
  },
  animate: { 
    opacity: 1, 
    scale: 1,
    rotate: 0,
    transition: {
      duration: 0.7,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const slideInUp: Variants = {
  initial: { 
    opacity: 0, 
    y: 100,
    scale: 0.9
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.9,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Stagger container for multiple items
export const staggerContainer: Variants = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.15,
      delayChildren: 0.2
    }
  }
}

export const staggerItem: Variants = {
  initial: { 
    opacity: 0, 
    y: 40,
    scale: 0.95
  },
  animate: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Floating animation for hero elements
export const floating: Variants = {
  initial: { y: 0 },
  animate: {
    y: [-10, 10, -10],
    transition: {
      duration: 6,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}

// Pulse animation for attention-grabbing elements
export const pulse: Variants = {
  initial: { scale: 1 },
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}

// Hover animations
export const hoverScale = {
  whileHover: {
    scale: 1.05,
    y: -5,
    transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
  },
  whileTap: {
    scale: 0.98,
    transition: { duration: 0.1 }
  }
}

export const hoverRotate = {
  whileHover: {
    scale: 1.1,
    rotate: 5,
    transition: { duration: 0.3, ease: [0.25, 0.46, 0.45, 0.94] }
  },
  whileTap: {
    scale: 0.95,
    rotate: 0,
    transition: { duration: 0.1 }
  }
}

export const hoverGlow = {
  whileHover: { 
    scale: 1.02,
    boxShadow: "0 20px 40px rgba(34, 197, 94, 0.3)",
    transition: { duration: 0.3, ease: "easeOut" }
  }
}

// Text reveal animation
export const textReveal: Variants = {
  initial: { 
    opacity: 0,
    y: 50,
    skewY: 10
  },
  animate: { 
    opacity: 1,
    y: 0,
    skewY: 0,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Page transition
export const pageTransition: Variants = {
  initial: { 
    opacity: 0,
    scale: 0.98,
    y: 20
  },
  animate: { 
    opacity: 1,
    scale: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  },
  exit: { 
    opacity: 0,
    scale: 0.98,
    y: -20,
    transition: {
      duration: 0.4,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

// Scroll-triggered animations
export const scrollReveal: Variants = {
  initial: { 
    opacity: 0, 
    y: 60,
    scale: 0.95
  },
  whileInView: { 
    opacity: 1, 
    y: 0,
    scale: 1,
    transition: {
      duration: 0.8,
      ease: [0.25, 0.46, 0.45, 0.94]
    }
  }
}

export const scrollStagger: Variants = {
  initial: {},
  whileInView: {
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1
    }
  }
}
