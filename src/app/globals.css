@import "tailwindcss";

:root {
  /* 🌱 HSSL Organic Eco-Friendly Color System */
  --background: #ffffff;
  --foreground: #0f172a;

  /* Primary Brand Colors - Emerald (Main Actions) */
  --primary: #059669;
  --primary-foreground: #ffffff;
  --primary-50: #ecfdf5;
  --primary-100: #d1fae5;
  --primary-200: #a7f3d0;
  --primary-300: #6ee7b7;
  --primary-400: #34d399;
  --primary-500: #10b981;
  --primary-600: #059669;
  --primary-700: #047857;
  --primary-800: #065f46;
  --primary-900: #064e3b;

  /* Secondary Brand Colors - Teal (Supporting Actions) */
  --secondary: #0d9488;
  --secondary-foreground: #ffffff;
  --secondary-50: #f0fdfa;
  --secondary-100: #ccfbf1;
  --secondary-200: #99f6e4;
  --secondary-300: #5eead4;
  --secondary-400: #2dd4bf;
  --secondary-500: #14b8a6;
  --secondary-600: #0d9488;
  --secondary-700: #0f766e;
  --secondary-800: #115e59;
  --secondary-900: #134e4a;

  /* Success Colors - Cool Emerald */
  --success: #10b981;
  --success-50: #ecfdf5;
  --success-100: #d1fae5;
  --success-200: #a7f3d0;
  --success-300: #6ee7b7;
  --success-400: #34d399;
  --success-500: #10b981;
  --success-600: #059669;
  --success-700: #047857;
  --success-800: #065f46;
  --success-900: #064e3b;

  /* Warning Colors - Earthy Amber */
  --warning: #f59e0b;
  --warning-50: #fffbeb;
  --warning-100: #fef3c7;
  --warning-200: #fde68a;
  --warning-300: #fcd34d;
  --warning-400: #fbbf24;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;
  --warning-800: #92400e;
  --warning-900: #78350f;

  /* Error Colors - Muted Rust */
  --error: #ef4444;
  --error-50: #fef2f2;
  --error-100: #fee2e2;
  --error-200: #fecaca;
  --error-300: #fca5a5;
  --error-400: #f87171;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;
  --error-800: #991b1b;
  --error-900: #7f1d1d;

  /* Accent Colors - Deep Forest Green */
  --accent: #15803d;
  --accent-50: #f0fdf4;
  --accent-100: #dcfce7;
  --accent-200: #bbf7d0;
  --accent-300: #86efac;
  --accent-400: #4ade80;
  --accent-500: #22c55e;
  --accent-600: #16a34a;
  --accent-700: #15803d;
  --accent-800: #166534;
  --accent-900: #14532d;

  /* Neutral Colors - Sage and Warm Grays */
  --neutral: #64748b;
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;

  /* Gray Scale - Warm Tones */
  --gray-50: #fafaf9;
  --gray-100: #f5f5f4;
  --gray-200: #e7e5e4;
  --gray-300: #d6d3d1;
  --gray-400: #a8a29e;
  --gray-500: #78716c;
  --gray-600: #57534e;
  --gray-700: #44403c;
  --gray-800: #292524;
  --gray-900: #1c1917;

  /* Spacing Scale */
  --spacing-xs: 0.25rem;   /* 4px */
  --spacing-sm: 0.5rem;    /* 8px */
  --spacing-md: 1rem;      /* 16px */
  --spacing-lg: 1.5rem;    /* 24px */
  --spacing-xl: 2rem;      /* 32px */
  --spacing-2xl: 3rem;     /* 48px */
  --spacing-3xl: 4rem;     /* 64px */
  --spacing-4xl: 6rem;     /* 96px */

  /* Typography Scale */
  --text-xs: 0.75rem;      /* 12px */
  --text-sm: 0.875rem;     /* 14px */
  --text-base: 1rem;       /* 16px */
  --text-lg: 1.125rem;     /* 18px */
  --text-xl: 1.25rem;      /* 20px */
  --text-2xl: 1.5rem;      /* 24px */
  --text-3xl: 1.875rem;    /* 30px */
  --text-4xl: 2.25rem;     /* 36px */
  --text-5xl: 3rem;        /* 48px */
  --text-6xl: 3.75rem;     /* 60px */

  /* Border Radius */
  --radius-sm: 0.25rem;    /* 4px */
  --radius-md: 0.375rem;   /* 6px */
  --radius-lg: 0.5rem;     /* 8px */
  --radius-xl: 0.75rem;    /* 12px */
  --radius-2xl: 1rem;      /* 16px */
  --radius-3xl: 1.5rem;    /* 24px */

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Utilities */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-600);
  border-radius: var(--radius-md);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-700);
}

/* 🎨 Enhanced Focus Styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2;
}

.focus-ring-secondary {
  @apply focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2;
}

.focus-ring-success {
  @apply focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2;
}

.focus-ring-warning {
  @apply focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2;
}

.focus-ring-error {
  @apply focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

/* 🌈 Gradient Utilities */
.gradient-hero-primary {
  background: linear-gradient(135deg, #10b981 0%, #14b8a6 50%, #16a34a 100%);
}

.gradient-hero-secondary {
  background: linear-gradient(135deg, #2dd4bf 0%, #34d399 50%, #84cc16 100%);
}

.gradient-card-subtle {
  background: linear-gradient(135deg, #ecfdf5 0%, #ffffff 50%, #f0fdfa 100%);
}

.gradient-interactive-primary {
  background: linear-gradient(135deg, #10b981 0%, #0d9488 100%);
}

.gradient-interactive-hover {
  background: linear-gradient(135deg, #059669 0%, #0f766e 100%);
}

/* 🃏 Card Component Styles */
.card-default {
  @apply bg-white border border-slate-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200;
}

.card-gradient {
  @apply bg-gradient-to-br from-white to-emerald-50 border border-emerald-100 rounded-lg shadow-sm hover:shadow-lg transition-all duration-200;
}

.card-elevated {
  @apply bg-white border border-slate-200 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200;
}

.card-emerald {
  @apply bg-emerald-50 border border-emerald-200 text-emerald-900 rounded-lg hover:bg-emerald-100 hover:border-emerald-300 transition-all duration-200;
}

.card-teal {
  @apply bg-teal-50 border border-teal-200 text-teal-900 rounded-lg hover:bg-teal-100 hover:border-teal-300 transition-all duration-200;
}

/* 🔘 Button Component Styles */
.btn-primary {
  @apply bg-emerald-600 text-white border-emerald-600 hover:bg-emerald-700 hover:border-emerald-700 active:bg-emerald-800 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:bg-emerald-300 disabled:border-emerald-300 transition-all duration-200;
}

.btn-secondary {
  @apply bg-teal-100 text-teal-800 border-teal-200 hover:bg-teal-200 hover:border-teal-300 active:bg-teal-300 focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 disabled:bg-teal-50 disabled:text-teal-400 transition-all duration-200;
}

.btn-outline {
  @apply bg-transparent text-emerald-600 border-emerald-600 hover:bg-emerald-50 hover:border-emerald-700 hover:text-emerald-700 active:bg-emerald-100 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:text-emerald-300 disabled:border-emerald-300 transition-all duration-200;
}

.btn-ghost {
  @apply bg-transparent text-emerald-600 border-transparent hover:bg-emerald-50 hover:text-emerald-700 active:bg-emerald-100 focus:ring-2 focus:ring-emerald-500 focus:ring-offset-2 disabled:text-emerald-300 transition-all duration-200;
}

/* 📱 Responsive Design Utilities */
.section-padding {
  @apply py-16 lg:py-24;
}

.section-padding-lg {
  @apply py-24 lg:py-32;
}

.container-responsive {
  @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
}

/* Performance optimizations */
* {
  box-sizing: border-box;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Animation utilities */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

/* Optimized animations using transform and opacity only */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateZ(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}

/* Hardware acceleration for smooth animations */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

@media (prefers-reduced-motion: reduce) {
  html {
    scroll-behavior: auto;
  }
}

/* Smooth transitions */
* {
  transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease;
}

/* Focus styles for accessibility */
.focus-visible:focus {
  outline: 2px solid #16a34a;
  outline-offset: 2px;
}

/* Line clamp utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
